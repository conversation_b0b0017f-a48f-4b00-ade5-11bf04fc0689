import { api } from './api';
import { 
  Conversation,
  Message,
  ApiResponse,
  ConversationCreateData,
  MessageCreateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

export const conversationService = {
  // Admin conversation endpoints
  adminGetConversations: async (params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Conversation>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = queryString ? `/conversations/admin?${queryString}` : '/conversations/admin';
    
    return api.get<PaginatedResponse<Conversation>>(url);
  },

  adminGetConversation: async (id: string): Promise<ApiResponse<Conversation>> => {
    return api.get<Conversation>(`/conversations/admin/${id}`);
  },

  adminCreateConversation: async (data: ConversationCreateData): Promise<ApiResponse<Conversation>> => {
    return api.post<Conversation>('/conversations/admin', data);
  },

  adminSendMessage: async (conversationId: string, content: string): Promise<ApiResponse<Message>> => {
    return api.post<Message>(`/conversations/admin/${conversationId}/messages`, { content });
  },

  adminLeaveConversation: async (id: string): Promise<ApiResponse<void>> => {
    return api.put<void>(`/conversations/admin/${id}/leave`);
  },

  // Teacher conversation endpoints
  teacherGetConversations: async (params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Conversation>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = queryString ? `/conversations/teacher?${queryString}` : '/conversations/teacher';
    
    return api.get<PaginatedResponse<Conversation>>(url);
  },

  teacherGetConversation: async (id: string): Promise<ApiResponse<Conversation>> => {
    return api.get<Conversation>(`/conversations/teacher/${id}`);
  },

  teacherCreateConversation: async (data: ConversationCreateData): Promise<ApiResponse<Conversation>> => {
    return api.post<Conversation>('/conversations/teacher', data);
  },

  teacherSendMessage: async (conversationId: string, content: string): Promise<ApiResponse<Message>> => {
    return api.post<Message>(`/conversations/teacher/${conversationId}/messages`, { content });
  },

  teacherLeaveConversation: async (id: string): Promise<ApiResponse<void>> => {
    return api.put<void>(`/conversations/teacher/${id}/leave`);
  },

  // Student conversation endpoints
  studentGetConversations: async (params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Conversation>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = queryString ? `/conversations/student?${queryString}` : '/conversations/student';
    
    return api.get<PaginatedResponse<Conversation>>(url);
  },

  studentGetConversation: async (id: string): Promise<ApiResponse<Conversation>> => {
    return api.get<Conversation>(`/conversations/student/${id}`);
  },

  studentCreateConversation: async (data: ConversationCreateData): Promise<ApiResponse<Conversation>> => {
    return api.post<Conversation>('/conversations/student', data);
  },

  studentSendMessage: async (conversationId: string, content: string): Promise<ApiResponse<Message>> => {
    return api.post<Message>(`/conversations/student/${conversationId}/messages`, { content });
  },

  studentLeaveConversation: async (id: string): Promise<ApiResponse<void>> => {
    return api.put<void>(`/conversations/student/${id}/leave`);
  },
};
