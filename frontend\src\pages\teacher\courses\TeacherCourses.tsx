import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, App, Pagination } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useApi, useMutation } from "@/hooks";
import { teacherService } from "@/services";
import { Course, QueryParams } from "@/common/types";
import { CourseList, CourseFilter, CreateCourseModal } from "./components";

const { Title } = Typography;

const TeacherCourses: React.FC = () => {
  const { notification } = App.useApp();
  // State
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [filterParams, setFilterParams] = useState<QueryParams>({});
  const [isModalVisible, setIsModalVisible] = useState(false);

  // API calls
  const coursesQuery = useApi(
    () =>
      teacherService.getMyCourses({
        page: currentPage,
        limit: pageSize,
        ...filterParams,
      }),
    {
      immediate: true,
      dependencies: [currentPage, pageSize, filterParams],
    }
  );

  const createCourseMutation = useMutation(teacherService.createCourse, {
    onSuccess: () => {
      notification.success({
        message: "Success",
        description: "Course created successfully.",
      });
      setIsModalVisible(false);
      coursesQuery.refetch();
    },
    onError: () => {
      notification.error({
        message: "Error",
        description: "Failed to create course. Please try again.",
      });
    },
  });

  // Extract data
  const courses = coursesQuery.data?.data?.items || [];
  const totalCourses = coursesQuery.data?.data?.total || 0;

  // Handlers
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  const handleFilter = (values: any) => {
    const params: QueryParams = {};

    if (values.search) {
      params.search = values.search;
    }

    if (values.departmentId) {
      params.departmentId = values.departmentId;
    }

    setFilterParams(params);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleResetFilter = () => {
    setFilterParams({});
    setCurrentPage(1);
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleCreateCourse = async (values: any) => {
    try {
      await createCourseMutation.mutateAsync(values);
    } catch (error) {
      // Error handling is done in the mutation onError callback
      console.error("Create course error:", error);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>My Courses</Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
          Create Course
        </Button>
      </div>

      <CourseFilter onSearch={handleFilter} onReset={handleResetFilter} />

      <CourseList courses={courses} loading={coursesQuery.loading} />

      {totalCourses > 0 && (
        <div className="flex justify-center mt-6">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCourses}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `Total ${total} courses`}
          />
        </div>
      )}

      <CreateCourseModal
        visible={isModalVisible}
        onCancel={handleCancel}
        onSubmit={handleCreateCourse}
        isSubmitting={createCourseMutation.loading}
      />
    </div>
  );
};

export default TeacherCourses;
