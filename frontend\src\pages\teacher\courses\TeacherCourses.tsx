import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, App } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useApi, useMutation } from "@/hooks";
import { teacherService, courseService } from "@/services";
import { CourseList, CourseFilter, CreateCourseModal } from "./components";

const { Title } = Typography;

const TeacherCourses: React.FC = () => {
  const { notification } = App.useApp();
  // State
  const [isModalVisible, setIsModalVisible] = useState(false);

  // API calls
  const coursesQuery = useApi(() => teacherService.getMyCourses(), {
    immediate: true,
    dependencies: [],
  });

  const createCourseMutation = useMutation(courseService.createCourse, {
    onSuccess: () => {
      notification.success({
        message: "Success",
        description: "Course created successfully.",
      });
      setIsModalVisible(false);
      coursesQuery.refetch();
    },
    onError: () => {
      notification.error({
        message: "Error",
        description: "Failed to create course. Please try again.",
      });
    },
  });

  // Extract data
  const courses = coursesQuery.data?.data || [];

  // Handlers
  const handleFilter = (values: any) => {
    // TODO: Implement client-side filtering if needed
    console.log("Filter values:", values);
  };

  const handleResetFilter = () => {
    // TODO: Implement client-side filter reset if needed
    console.log("Reset filters");
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleCreateCourse = async (values: any) => {
    try {
      await createCourseMutation.mutateAsync(values);
    } catch (error) {
      // Error handling is done in the mutation onError callback
      console.error("Create course error:", error);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>My Courses</Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
          Create Course
        </Button>
      </div>

      <CourseFilter onSearch={handleFilter} onReset={handleResetFilter} />

      <CourseList courses={courses} loading={coursesQuery.loading} />

      <CreateCourseModal
        visible={isModalVisible}
        onCancel={handleCancel}
        onSubmit={handleCreateCourse}
        isSubmitting={createCourseMutation.loading}
      />
    </div>
  );
};

export default TeacherCourses;
