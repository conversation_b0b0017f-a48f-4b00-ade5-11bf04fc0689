import { api } from './api';
import { 
  Course,
  Resource,
  Student,
  Enrollment,
  ApiResponse,
  CourseCreateData,
  CourseUpdateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  departmentId?: string;
  teacherId?: string;
  isPublic?: boolean;
}

export const courseService = {
  // Public course endpoints
  getCourses: async (params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Course>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.departmentId) searchParams.append('departmentId', params.departmentId);
    if (params.teacherId) searchParams.append('teacherId', params.teacherId);
    if (params.isPublic !== undefined) searchParams.append('isPublic', params.isPublic.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/course?${queryString}` : '/course';
    
    return api.get<PaginatedResponse<Course>>(url);
  },

  getCourse: async (id: string): Promise<ApiResponse<Course>> => {
    return api.get<Course>(`/course/${id}`);
  },

  // Teacher course management endpoints
  createCourse: async (data: CourseCreateData): Promise<ApiResponse<Course>> => {
    return api.post<Course>('/course', data);
  },

  updateCourse: async (id: string, data: CourseUpdateData): Promise<ApiResponse<Course>> => {
    return api.put<Course>(`/course/${id}`, data);
  },

  deleteCourse: async (id: string): Promise<ApiResponse<void>> => {
    return api.delete<void>(`/course/${id}`);
  },

  getCourseResources: async (id: string, params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Resource>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = queryString ? `/course/${id}/resources?${queryString}` : `/course/${id}/resources`;
    
    return api.get<PaginatedResponse<Resource>>(url);
  },

  getEnrolledStudents: async (id: string, params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Student>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = queryString ? `/course/${id}/students?${queryString}` : `/course/${id}/students`;
    
    return api.get<PaginatedResponse<Student>>(url);
  },

  updateEnrollmentStatus: async (
    courseId: string, 
    enrollmentId: string, 
    status: string
  ): Promise<ApiResponse<Enrollment>> => {
    return api.put<Enrollment>(`/course/${courseId}/enrollments/${enrollmentId}`, { status });
  },
};
