{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "^5.74.11", "@tanstack/react-query-devtools": "^5.75.0", "antd": "^5.24.2", "axios": "^1.8.1", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.2.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/lodash": "^4.17.16", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.4.35", "sass-embedded": "^1.88.0", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}