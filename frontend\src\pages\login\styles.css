/* Login Type Selector Styles */
.login-type-selector .ant-radio-button-wrapper {
  transition: all 0.3s ease;
}

.login-type-selector .ant-radio-button-wrapper:hover {
  transform: translateY(-2px);
}

.login-type-selector .ant-radio-button-wrapper-checked {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Glassmorphism effects */
.glass-login-selector {
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Login Panel Animations */
.login-panel {
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
  transform-origin: left center;
  display: flex;
  flex-direction: column;
}

.login-form {
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
  transform-origin: right center;
  display: flex;
  flex-direction: column;
}

/* Student Login Animation */
.login-panel-student {
  animation: slideInLeft 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

.login-form-student {
  animation: slideInRight 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

/* Teacher Login Animation */
.login-panel-teacher {
  animation: slideInLeft 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

.login-form-teacher {
  animation: slideInRight 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

/* Admin Login Animation */
.login-panel-admin {
  animation: slideInLeft 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

.login-form-admin {
  animation: slideInRight 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

/* Exit Animations */
.login-panel.exit-left {
  animation: slideOutLeft 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

.login-form.exit-right {
  animation: slideOutRight 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

/* Animation Keyframes */
@keyframes slideInLeft {
  0% {
    transform: translateX(-100%) scale(1.2);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%) scale(1.2);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  0% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%) scale(0.8);
    opacity: 0;
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
  }
}

/* Fix for height issues */
.ant-layout, .ant-layout-content {
  height: 100%;
}

/* Responsive Adjustments */
@media (max-width: 1023px) {
  .login-panel, .login-form {
    transform-origin: center;
  }
  
  .login-panel {
    animation: fadeInDown 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
  }
  
  .login-form {
    animation: fadeInUp 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
  }
  
  .login-panel.exit-left {
    animation: fadeOutUp 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
  }
  
  .login-form.exit-right {
    animation: fadeOutDown 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
  }
  
  @keyframes fadeInDown {
    0% {
      transform: translateY(-50px) scale(1.1);
      opacity: 0;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
  
  @keyframes fadeInUp {
    0% {
      transform: translateY(50px) scale(1.1);
      opacity: 0;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
  
  @keyframes fadeOutUp {
    0% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateY(-50px) scale(0.9);
      opacity: 0;
    }
  }
  
  @keyframes fadeOutDown {
    0% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateY(50px) scale(0.9);
      opacity: 0;
    }
  }
}

/* Responsive Adjustments for Exit Animations */
@media (max-width: 1023px) {
  .login-panel.exit-left {
    animation: fadeOutUp 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
  }
  
  .login-form.exit-right {
    animation: fadeOutDown 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
  }
  
  @keyframes fadeOutUp {
    0% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateY(-50px) scale(0.9);
      opacity: 0;
    }
  }
  
  @keyframes fadeOutDown {
    0% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateY(50px) scale(0.9);
      opacity: 0;
    }
  }
} 