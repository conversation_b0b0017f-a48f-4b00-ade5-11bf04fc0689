import { api } from './api';
import { 
  Department,
  ApiResponse,
  DepartmentCreateData,
  DepartmentUpdateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

export const departmentService = {
  // Get all departments (with pagination)
  getDepartments: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Department>>> =>
    api.get('/departments', { params }),

  // Get all departments (without pagination - for dropdowns)
  getAllDepartments: (): Promise<ApiResponse<Department[]>> =>
    api.get('/departments/all'),

  // Get department by ID
  getDepartmentById: (id: string): Promise<ApiResponse<Department>> =>
    api.get(`/departments/${id}`),

  // Create department (Admin only)
  createDepartment: (data: DepartmentCreateData): Promise<ApiResponse<Department>> =>
    api.post('/admin/departments', data),

  // Update department (Admin only)
  updateDepartment: (id: string, data: DepartmentUpdateData): Promise<ApiResponse<Department>> =>
    api.put(`/admin/departments/${id}`, data),

  // Delete department (Admin only)
  deleteDepartment: (id: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/admin/departments/${id}`),
};
