import { api } from './api';
import { 
  LoginCredentials, 
  Admin, 
  Teacher, 
  Student,
  ApiResponse 
} from '../common/types';

export type UserType = 'admin' | 'teacher' | 'student';

export interface LoginResponse {
  user: Admin | Teacher | Student;
  userType: UserType;
}

export interface SignupData {
  name: string;
  email: string;
  password: string;
  phoneNumber?: string;
  qualification?: string; // For teachers
  specialization?: string; // For teachers
  departmentId?: string; // For teachers and students
  enrollmentNo?: string; // For students
}

export interface VerifyEmailData {
  email: string;
  otp: string;
}

export interface ForgotPasswordData {
  email: string;
  userType: UserType;
}

export interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
  userType: UserType;
}

export interface RefreshTokenResponse {
  user: Admin | Teacher | Student;
  userType: UserType;
}

export const authService = {
  // Admin Authentication
  adminLogin: (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> =>
    api.post('/auth/admin/login', credentials),

  // Teacher Authentication
  teacherLogin: (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> =>
    api.post('/auth/teacher/login', credentials),
  
  teacherSignup: (data: SignupData): Promise<ApiResponse<LoginResponse>> =>
    api.post('/auth/teacher/signup', data),

  // Student Authentication
  studentLogin: (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> =>
    api.post('/auth/student/login', credentials),
  
  studentSignup: (data: SignupData): Promise<ApiResponse<LoginResponse>> =>
    api.post('/auth/student/signup', data),

  // Email Verification
  verifyEmail: (data: VerifyEmailData): Promise<ApiResponse<LoginResponse>> =>
    api.post('/auth/verify-email', data),

  resendOtp: (email: string): Promise<ApiResponse<{ message: string }>> =>
    api.post('/auth/resend-otp', { email }),

  // Password Reset
  forgotPassword: (data: ForgotPasswordData): Promise<ApiResponse<{ message: string }>> =>
    api.post('/auth/forgot-password', data),

  resetPassword: (data: ResetPasswordData): Promise<ApiResponse<{ message: string }>> =>
    api.post('/auth/reset-password', data),

  // Token Management
  refreshToken: (): Promise<ApiResponse<RefreshTokenResponse>> =>
    api.post('/auth/refresh-token'),

  logout: (): Promise<ApiResponse<{ message: string }>> =>
    api.post('/auth/logout'),

  // Profile Management
  getProfile: (): Promise<ApiResponse<Admin | Teacher | Student>> =>
    api.get('/auth/profile'),
};
