import { api } from './api';
import { 
  Student,
  Course,
  Resource,
  Enrollment,
  ResourceComment,
  NewsEvent,
  NewsEventComment,
  ApiResponse
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  departmentId?: string;
  courseId?: string;
  type?: string;
  isPublic?: boolean;
}

export const studentService = {
  // Profile Management
  getProfile: (): Promise<ApiResponse<Student>> =>
    api.get('/student/profile'),

  updateProfile: (data: Partial<Student>): Promise<ApiResponse<Student>> =>
    api.put('/student/profile', data),

  // Course Management
  getEnrolledCourses: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Course>>> =>
    api.get('/student/courses', { params }),

  getCourseById: (courseId: string): Promise<ApiResponse<Course>> =>
    api.get(`/student/courses/${courseId}`),

  enrollInCourse: (courseId: string): Promise<ApiResponse<Enrollment>> =>
    api.post(`/student/courses/${courseId}/enroll`),

  withdrawFromCourse: (courseId: string): Promise<ApiResponse<{ message: string }>> =>
    api.put(`/student/courses/${courseId}/withdraw`),

  // Resource Management
  getCourseResources: (courseId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Resource>>> =>
    api.get(`/student/courses/${courseId}/resources`, { params }),

  // Resource Comments
  addResourceComment: (resourceId: string, content: string): Promise<ApiResponse<ResourceComment>> =>
    api.post(`/student/resources/${resourceId}/comments`, { content }),

  updateResourceComment: (commentId: string, content: string): Promise<ApiResponse<ResourceComment>> =>
    api.put(`/student/resources/comments/${commentId}`, { content }),

  deleteResourceComment: (commentId: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/student/resources/comments/${commentId}`),

  // News/Events (Note: These endpoints don't exist in backend yet)
  getNewsEvents: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<NewsEvent>>> =>
    api.get('/student/news', { params }),

  getNewsEventById: (id: string): Promise<ApiResponse<NewsEvent>> =>
    api.get(`/student/news/${id}`),

  // News/Event Comments (Note: These endpoints don't exist in backend yet)
  getNewsEventComments: (newsEventId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<NewsEventComment>>> =>
    api.get(`/student/news/${newsEventId}/comments`, { params }),

  addNewsEventComment: (newsEventId: string, content: string): Promise<ApiResponse<NewsEventComment>> =>
    api.post(`/student/news/${newsEventId}/comments`, { content }),

  updateNewsEventComment: (commentId: string, content: string): Promise<ApiResponse<NewsEventComment>> =>
    api.put(`/student/news/comments/${commentId}`, { content }),

  deleteNewsEventComment: (commentId: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/student/news/comments/${commentId}`),

  // Dashboard (Note: This endpoint doesn't exist in backend yet)
  getDashboardStats: (): Promise<ApiResponse<{
    totalEnrolledCourses: number;
    totalAvailableCourses: number;
    recentCourses: Course[];
    recentResources: Resource[];
    recentNewsEvents: NewsEvent[];
  }>> =>
    api.get('/student/dashboard/stats'),
};
