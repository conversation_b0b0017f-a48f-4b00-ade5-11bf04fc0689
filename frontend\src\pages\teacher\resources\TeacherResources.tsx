import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, App, Pagination, Modal } from "antd";
import { PlusOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { useApi, useMutation } from "@/hooks";
import { teacherService, resourceService } from "@/services";
import { Resource, QueryParams } from "@/common/types";
import {
  ResourceList,
  ResourceFilter,
  CreateResourceModal,
} from "./components";

const { Title } = Typography;
const { confirm } = Modal;

const TeacherResources: React.FC = () => {
  const { notification } = App.useApp();
  // State
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [filterParams, setFilterParams] = useState<QueryParams>({});
  const [isModalVisible, setIsModalVisible] = useState(false);

  // API calls
  const resourcesQuery = useApi(
    () =>
      teacherService.getMyResources({
        page: currentPage,
        limit: pageSize,
        ...filterParams,
      }),
    {
      immediate: true,
      dependencies: [currentPage, pageSize, filterParams],
    }
  );

  const createResourceMutation = useMutation(resourceService.createResource, {
    onSuccess: () => {
      notification.success({
        message: "Success",
        description: "Resource created successfully.",
      });
      setIsModalVisible(false);
      resourcesQuery.refetch();
    },
    onError: () => {
      notification.error({
        message: "Error",
        description: "Failed to create resource. Please try again.",
      });
    },
  });

  const deleteResourceMutation = useMutation(resourceService.deleteResource, {
    onSuccess: () => {
      notification.success({
        message: "Success",
        description: "Resource deleted successfully.",
      });
      resourcesQuery.refetch();
    },
    onError: () => {
      notification.error({
        message: "Error",
        description: "Failed to delete resource. Please try again.",
      });
    },
  });

  // Extract data
  const responseData = resourcesQuery.data?.data as any;
  const resources = responseData?.items || [];
  const pagination = responseData?.pagination || {
    total: 0,
    page: 1,
    limit: 12,
    hasMore: false,
  };

  // Handlers
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  const handleFilter = (values: any) => {
    const params: QueryParams = {};

    if (values.search) {
      params.search = values.search;
    }

    if (values.courseId) {
      params.courseId = values.courseId;
    }

    if (values.type) {
      params.type = values.type;
    }

    if (values.isPublic) {
      params.isPublic = values.isPublic;
    }

    setFilterParams(params);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleResetFilter = () => {
    setFilterParams({});
    setCurrentPage(1);
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleCreateResource = async (formData: FormData) => {
    try {
      await createResourceMutation.mutateAsync(formData);
    } catch (error) {
      // Error handling is done in the mutation onError callback
      console.error("Create resource error:", error);
    }
  };

  const handleDeleteResource = (id: string) => {
    confirm({
      title: "Are you sure you want to delete this resource?",
      icon: <ExclamationCircleOutlined />,
      content: "This action cannot be undone.",
      okText: "Yes, Delete",
      okType: "danger",
      cancelText: "Cancel",
      onOk: async () => {
        try {
          await deleteResourceMutation.mutateAsync(id);
        } catch (error) {
          // Error handling is done in the mutation onError callback
          console.error("Delete resource error:", error);
        }
      },
    });
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>My Resources</Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
          Create Resource
        </Button>
      </div>

      <ResourceFilter onSearch={handleFilter} onReset={handleResetFilter} />

      <ResourceList
        resources={resources}
        loading={resourcesQuery.loading}
        onDelete={handleDeleteResource}
      />

      {pagination.total > 0 && (
        <div className="flex justify-center mt-6">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={pagination.total}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `Total ${total} resources`}
          />
        </div>
      )}

      <CreateResourceModal
        visible={isModalVisible}
        onCancel={handleCancel}
        onSubmit={handleCreateResource}
        isSubmitting={createResourceMutation.loading}
      />
    </div>
  );
};

export default TeacherResources;
