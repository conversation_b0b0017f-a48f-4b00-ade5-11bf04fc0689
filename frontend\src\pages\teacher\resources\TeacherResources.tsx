import React, { useState, useEffect } from "react";
import { Ty<PERSON><PERSON>, <PERSON><PERSON>, App, Pagination, Modal } from "antd";
import { PlusOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { useApi, useMutation } from "@/hooks";
import { teacherService } from "@/services";
import { Resource, QueryParams, ResourceCreateData } from "@/common/types";
import {
  ResourceList,
  ResourceFilter,
  CreateResourceModal,
} from "./components";

const { Title } = Typography;
const { confirm } = Modal;

const TeacherResources: React.FC = () => {
  const { notification } = App.useApp();
  // State
  const [resources, setResources] = useState<Resource[]>([]);
  const [totalResources, setTotalResources] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [filterParams, setFilterParams] = useState<QueryParams>({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState<string | null>(null);

  // API calls
  const coursesQuery = useApi(
    () => teacherService.getMyCourses({ limit: 100 }),
    { immediate: true }
  );

  const createResourceMutation = useMutation(teacherService.createResource, {
    onSuccess: () => {
      notification.success({
        message: "Success",
        description: "Resource created successfully.",
      });
      setIsModalVisible(false);
      coursesQuery.refetch();
    },
    onError: () => {
      notification.error({
        message: "Error",
        description: "Failed to create resource. Please try again.",
      });
    },
  });

  const deleteResourceMutation = useMutation(teacherService.deleteResource, {
    onSuccess: () => {
      notification.success({
        message: "Success",
        description: "Resource deleted successfully.",
      });
      coursesQuery.refetch();
    },
    onError: () => {
      notification.error({
        message: "Error",
        description: "Failed to delete resource. Please try again.",
      });
    },
  });

  // Fetch resources from all courses
  useEffect(() => {
    if (coursesQuery.data?.data?.items) {
      const courses = coursesQuery.data.data.items;
      let allResources: Resource[] = [];

      // Extract resources from all courses
      courses.forEach((course) => {
        if (course.resources) {
          allResources = [...allResources, ...course.resources];
        }
      });

      // Apply filters
      if (filterParams.search) {
        allResources = allResources.filter((resource) =>
          resource.title
            .toLowerCase()
            .includes(filterParams.search!.toLowerCase())
        );
      }

      if (filterParams.courseId) {
        allResources = allResources.filter(
          (resource) => resource.courseId === filterParams.courseId
        );
      }

      if (filterParams.type) {
        allResources = allResources.filter(
          (resource) => resource.type === filterParams.type
        );
      }

      if (filterParams.isPublic) {
        allResources = allResources.filter(
          (resource) => resource.isPublic === true
        );
      }

      // Sort by creation date (newest first)
      allResources.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setTotalResources(allResources.length);

      // Apply pagination
      const start = (currentPage - 1) * pageSize;
      const end = start + pageSize;
      setResources(allResources.slice(start, end));
    }
  }, [coursesQuery.data, filterParams, currentPage, pageSize]);

  // Error handling is done in the API hook

  // Handlers
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  const handleFilter = (values: any) => {
    const params: QueryParams = {};

    if (values.search) {
      params.search = values.search;
    }

    if (values.courseId) {
      params.courseId = values.courseId;
    }

    if (values.type) {
      params.type = values.type;
    }

    if (values.isPublic) {
      params.isPublic = values.isPublic;
    }

    setFilterParams(params);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleResetFilter = () => {
    setFilterParams({});
    setCurrentPage(1);
  };

  const showCreateModal = () => {
    setIsCreateModalVisible(true);
  };

  const handleDeleteResource = (id: string) => {
    confirm({
      title: "Are you sure you want to delete this resource?",
      icon: <ExclamationCircleOutlined />,
      content: "This action cannot be undone.",
      okText: "Yes, Delete",
      okType: "danger",
      cancelText: "Cancel",
      onOk: async () => {
        try {
          await deleteResourceMutation.mutateAsync(id);
        } catch (error) {
          // Error handling is done in the mutation onError callback
          console.error("Delete resource error:", error);
        }
      },
    });
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>My Resources</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showCreateModal}
        >
          Create Resource
        </Button>
      </div>

      <ResourceFilter onSearch={handleFilter} onReset={handleResetFilter} />

      <ResourceList
        resources={resources}
        loading={resourcesQuery.loading}
        onView={handleViewResource}
        onEdit={handleEditResource}
        onDelete={handleDeleteResource}
      />

      {pagination.total > 0 && (
        <div className="flex justify-center mt-6">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={pagination.total}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `Total ${total} resources`}
          />
        </div>
      )}

      <CreateResourceModal
        visible={isCreateModalVisible}
        onCancel={handleCreateCancel}
        onSubmit={handleCreateResource}
        isSubmitting={createResourceMutation.loading}
      />

      <ViewResourceModal
        visible={isViewModalVisible}
        resource={selectedResource}
        loading={false}
        onCancel={handleViewCancel}
      />

      <EditResourceModal
        visible={isEditModalVisible}
        resource={selectedResource}
        onCancel={handleEditCancel}
        onSubmit={handleUpdateResource}
        isSubmitting={updateResourceMutation.loading}
      />
    </div>
  );
};

export default TeacherResources;
