import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  List,
  Avatar,
  Space,
  Input,
  Form,
  Pagination,
  Spin,
  Empty,
  Dropdown,
  App,
  Tag,
  Divider,
} from "antd";
import {
  ArrowLeftOutlined,
  MessageOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  UserOutlined,
  FileTextOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { useParams, useNavigate } from "react-router-dom";
import { useApi, useMutation } from "@/hooks";
import { studentService } from "@/services";
import { ROUTES } from "@/common/constants";
import { useAuth } from "@/context/AuthContext";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  student?: {
    id: string;
    name: string;
  };
  teacher?: {
    id: string;
    name: string;
  };
}

const StudentResourceComments: React.FC = () => {
  const { resourceId } = useParams<{ resourceId: string }>();
  const navigate = useNavigate();
  const { notification, modal } = App.useApp();
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // API queries - Note: We'll need to create these endpoints
  const resourceQuery = useApi(
    () =>
      resourceId
        ? studentService.getResourceDetails(resourceId)
        : Promise.reject("No resource ID"),
    {
      immediate: !!resourceId,
      dependencies: [resourceId],
    }
  );

  const commentsQuery = useApi(
    () =>
      resourceId
        ? studentService.getResourceComments(resourceId, {
            page: currentPage,
            limit: pageSize,
          })
        : Promise.reject("No resource ID"),
    {
      immediate: !!resourceId,
      dependencies: [resourceId, currentPage],
    }
  );

  // Mutations
  const createCommentMutation = useMutation(
    ({ resourceId, content }: { resourceId: string; content: string }) =>
      studentService.createResourceComment(resourceId, { content }),
    {
      onSuccess: () => {
        notification.success({
          message: "Success",
          description: "Comment added successfully.",
        });
        form.resetFields();
        commentsQuery.refetch();
      },
      onError: (error: any) => {
        notification.error({
          message: "Error",
          description:
            error.response?.data?.message || "Failed to add comment.",
        });
      },
    }
  );

  const updateCommentMutation = useMutation(
    ({ commentId, content }: { commentId: string; content: string }) =>
      studentService.updateResourceComment(commentId, { content }),
    {
      onSuccess: () => {
        notification.success({
          message: "Success",
          description: "Comment updated successfully.",
        });
        setEditingComment(null);
        editForm.resetFields();
        commentsQuery.refetch();
      },
      onError: (error: any) => {
        notification.error({
          message: "Error",
          description:
            error.response?.data?.message || "Failed to update comment.",
        });
      },
    }
  );

  const deleteCommentMutation = useMutation(
    studentService.deleteResourceComment,
    {
      onSuccess: () => {
        notification.success({
          message: "Success",
          description: "Comment deleted successfully.",
        });
        commentsQuery.refetch();
      },
      onError: (error: any) => {
        notification.error({
          message: "Error",
          description:
            error.response?.data?.message || "Failed to delete comment.",
        });
      },
    }
  );

  const resource = resourceQuery.data?.data;
  const commentsData = commentsQuery.data?.data;
  const comments = commentsData?.items || [];
  const pagination = commentsData?.pagination || {
    total: 0,
    page: 1,
    limit: pageSize,
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleViewResource = () => {
    if (resource?.url) {
      window.open(resource.url, "_blank");
    }
  };

  const handleSubmitComment = async (values: { content: string }) => {
    if (!resourceId) return;
    await createCommentMutation.mutateAsync({
      resourceId,
      content: values.content,
    });
  };

  const handleEditComment = (comment: Comment) => {
    setEditingComment(comment.id);
    editForm.setFieldsValue({ content: comment.content });
  };

  const handleUpdateComment = async (values: { content: string }) => {
    if (!editingComment) return;
    await updateCommentMutation.mutateAsync({
      commentId: editingComment,
      content: values.content,
    });
  };

  const handleDeleteComment = (commentId: string) => {
    modal.confirm({
      title: "Delete Comment",
      content:
        "Are you sure you want to delete this comment? This action cannot be undone.",
      onOk: () => deleteCommentMutation.mutateAsync(commentId),
    });
  };

  const handleCancelEdit = () => {
    setEditingComment(null);
    editForm.resetFields();
  };

  const getResourceTypeColor = (type: string) => {
    switch (type) {
      case "VIDEO":
        return "red";
      case "DOCUMENT":
        return "blue";
      case "LINK":
        return "green";
      default:
        return "default";
    }
  };

  const isCommentOwner = (comment: Comment) => {
    return comment.student?.id === user?.id;
  };

  const getCommentActions = (comment: Comment) => {
    if (!isCommentOwner(comment)) return [];

    const items = [
      {
        key: "edit",
        label: "Edit",
        icon: <EditOutlined />,
        onClick: () => handleEditComment(comment),
      },
      {
        key: "delete",
        label: "Delete",
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDeleteComment(comment.id),
      },
    ];

    return [
      <Dropdown key="more" menu={{ items }} trigger={["click"]}>
        <Button type="text" icon={<MoreOutlined />} size="small" />
      </Dropdown>,
    ];
  };

  if (resourceQuery.loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spin size="large" />
      </div>
    );
  }

  if (resourceQuery.error || !resource) {
    return (
      <div className="p-6">
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={handleBack}
          className="mb-4"
        >
          Back
        </Button>
        <Empty description="Resource not found or you don't have access to this resource" />
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div className="flex items-center gap-4">
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            Back
          </Button>
          <div>
            <Title level={2} className="mb-0">
              Resource Comments
            </Title>
          </div>
        </div>
        <Button
          type="primary"
          icon={<EyeOutlined />}
          onClick={handleViewResource}
        >
          View Resource
        </Button>
      </div>

      {/* Resource Info */}
      <Card className="mb-6">
        <Space direction="vertical" size="small" style={{ width: "100%" }}>
          <Space>
            <FileTextOutlined className="text-blue-500" />
            <Title level={4} className="mb-0">
              {resource.title}
            </Title>
            <Tag color={getResourceTypeColor(resource.type)}>
              {resource.type}
            </Tag>
          </Space>
          {resource.description && (
            <Paragraph type="secondary">{resource.description}</Paragraph>
          )}
          <Space>
            <Text type="secondary">Course: {resource.course?.name}</Text>
            <Divider type="vertical" />
            <Text type="secondary">By: {resource.teacher?.name}</Text>
            <Divider type="vertical" />
            <Text type="secondary">
              Created: {dayjs(resource.createdAt).format("MMM D, YYYY")}
            </Text>
          </Space>
        </Space>
      </Card>

      {/* Add Comment Form */}
      <Card title="Add a Comment" className="mb-6">
        <Form form={form} onFinish={handleSubmitComment} layout="vertical">
          <Form.Item
            name="content"
            rules={[
              { required: true, message: "Please enter your comment" },
              { max: 1000, message: "Comment cannot exceed 1000 characters" },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="Write your comment here..."
              showCount
              maxLength={1000}
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={createCommentMutation.loading}
              icon={<MessageOutlined />}
            >
              Add Comment
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* Comments List */}
      <Card title={`Comments (${pagination.total})`}>
        {commentsQuery.loading ? (
          <div className="flex justify-center py-8">
            <Spin size="large" />
          </div>
        ) : comments.length > 0 ? (
          <>
            <List
              dataSource={comments}
              renderItem={(comment: Comment) => (
                <List.Item actions={getCommentActions(comment)}>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <Space>
                        <Text strong>
                          {comment.student?.name ||
                            comment.teacher?.name ||
                            "Unknown User"}
                        </Text>
                        {comment.teacher && <Tag color="blue">Teacher</Tag>}
                        <Text type="secondary" className="text-sm">
                          {dayjs(comment.createdAt).fromNow()}
                        </Text>
                        {comment.updatedAt !== comment.createdAt && (
                          <Text type="secondary" className="text-xs">
                            (edited)
                          </Text>
                        )}
                      </Space>
                    }
                    description={
                      editingComment === comment.id ? (
                        <Form
                          form={editForm}
                          onFinish={handleUpdateComment}
                          className="mt-2"
                        >
                          <Form.Item
                            name="content"
                            rules={[
                              {
                                required: true,
                                message: "Please enter your comment",
                              },
                              {
                                max: 1000,
                                message:
                                  "Comment cannot exceed 1000 characters",
                              },
                            ]}
                          >
                            <TextArea rows={3} showCount maxLength={1000} />
                          </Form.Item>
                          <Form.Item className="mb-0">
                            <Space>
                              <Button
                                type="primary"
                                htmlType="submit"
                                size="small"
                                loading={updateCommentMutation.loading}
                              >
                                Update
                              </Button>
                              <Button size="small" onClick={handleCancelEdit}>
                                Cancel
                              </Button>
                            </Space>
                          </Form.Item>
                        </Form>
                      ) : (
                        <div className="mt-2">
                          <Paragraph className="mb-0">
                            {comment.content}
                          </Paragraph>
                        </div>
                      )
                    }
                  />
                </List.Item>
              )}
            />

            {pagination.total > pageSize && (
              <div className="flex justify-center mt-4">
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={pagination.total}
                  onChange={setCurrentPage}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total) => `Total ${total} comments`}
                />
              </div>
            )}
          </>
        ) : (
          <Empty description="No comments yet. Be the first to comment!" />
        )}
      </Card>
    </div>
  );
};

export default StudentResourceComments;
