// Re-export all services
export { api } from './api';
export { authService } from './authService';
export { adminService } from './adminService';
export { teacherService } from './teacherService';
export { studentService } from './studentService';
export { departmentService } from './departmentService';

// Re-export types
export type { UserType, LoginResponse, SignupData } from './authService';
export type { PaginatedResponse as AdminPaginatedResponse, QueryParams as AdminQueryParams } from './adminService';
export type { PaginatedResponse as TeacherPaginatedResponse, QueryParams as TeacherQueryParams } from './teacherService';
export type { PaginatedResponse as StudentPaginatedResponse, QueryParams as StudentQueryParams } from './studentService';
export type { PaginatedResponse as DepartmentPaginatedResponse, QueryParams as DepartmentQueryParams } from './departmentService';
