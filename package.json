{"name": "millat-vocational-training", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon --exec node --no-warnings=ExperimentalWarning --loader ts-node/esm backend/src/index.ts", "seed:admin": "node --loader ts-node/esm backend/src/db/seeders/adminSeeder.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.8", "@types/nodemailer": "^6.4.17", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "dependencies": {"@prisma/client": "^6.3.1", "@types/multer": "^1.4.12", "bcryptjs": "^3.0.0", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "prisma": "^6.3.1"}}