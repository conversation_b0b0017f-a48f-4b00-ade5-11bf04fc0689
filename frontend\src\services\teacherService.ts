import { api } from './api';
import {
  Teacher,
  Course,
  Resource,
  Student,
  Enrollment,
  ResourceComment,
  ApiResponse,
  ResourceCreateData,
  ResourceUpdateData
} from '../common/types';

export const teacherService = {
  // Profile Management
  getProfile: (): Promise<ApiResponse<Teacher>> =>
    api.get('/teacher/profile'),

  updateProfile: (data: Partial<Teacher>): Promise<ApiResponse<Teacher>> =>
    api.put('/teacher/profile', data),

  // Course Management
  getMyCourses: (): Promise<ApiResponse<Course[]>> =>
    api.get('/teacher/courses'),

  getCourseDetails: (id: string): Promise<ApiResponse<Course>> =>
    api.get(`/teacher/courses/${id}`),

  getCourseStudents: (id: string): Promise<ApiResponse<Student[]>> =>
    api.get(`/teacher/courses/${id}/students`),

  updateEnrollmentStatus: (courseId: string, enrollmentId: string, data: { status: string }): Promise<ApiResponse<Enrollment>> =>
    api.put(`/teacher/courses/${courseId}/enrollments/${enrollmentId}`, data),

  // Resource Management
  getCourseResources: (id: string): Promise<ApiResponse<Resource[]>> =>
    api.get(`/teacher/courses/${id}/resources`),

  createResource: (data: ResourceCreateData): Promise<ApiResponse<Resource>> =>
    api.post('/teacher/resources', data),

  updateResource: (id: string, data: ResourceUpdateData): Promise<ApiResponse<Resource>> =>
    api.put(`/teacher/resources/${id}`, data),

  deleteResource: (id: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/teacher/resources/${id}`),

  getResourceComments: (id: string): Promise<ApiResponse<ResourceComment[]>> =>
    api.get(`/teacher/resources/${id}/comments`),

  createResourceComment: (id: string, data: { content: string }): Promise<ApiResponse<ResourceComment>> =>
    api.post(`/teacher/resources/${id}/comments`, data),

  updateResourceComment: (commentId: string, data: { content: string }): Promise<ApiResponse<ResourceComment>> =>
    api.put(`/teacher/resources/comments/${commentId}`, data),

  deleteResourceComment: (commentId: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/teacher/resources/comments/${commentId}`),
};
