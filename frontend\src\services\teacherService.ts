import { api } from './api';
import { 
  Teacher,
  Course,
  Resource,
  Student,
  Enrollment,
  ResourceComment,
  ApiResponse,
  CourseCreateData,
  CourseUpdateData,
  ResourceCreateData,
  ResourceUpdateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  departmentId?: string;
  courseId?: string;
  type?: string;
  isPublic?: boolean;
}

export const teacherService = {
  // Profile Management
  getProfile: (): Promise<ApiResponse<Teacher>> =>
    api.get('/teacher/profile'),

  updateProfile: (data: Partial<Teacher>): Promise<ApiResponse<Teacher>> =>
    api.put('/teacher/profile', data),

  // Course Management
  getCourses: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Course>>> =>
    api.get('/teacher/courses', { params }),

  getCourseById: (id: string): Promise<ApiResponse<Course>> =>
    api.get(`/teacher/courses/${id}`),

  // Student Management for Courses
  getCourseStudents: (courseId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Student>>> =>
    api.get(`/teacher/courses/${courseId}/students`, { params }),

  updateEnrollmentStatus: (courseId: string, enrollmentId: string, status: string): Promise<ApiResponse<Enrollment>> =>
    api.put(`/teacher/courses/${courseId}/enrollments/${enrollmentId}`, { status }),

  // Resource Management
  getCourseResources: (courseId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Resource>>> =>
    api.get(`/teacher/courses/${courseId}/resources`, { params }),

  createResource: (data: ResourceCreateData): Promise<ApiResponse<Resource>> =>
    api.post('/teacher/resources', data),

  updateResource: (id: string, data: ResourceUpdateData): Promise<ApiResponse<Resource>> =>
    api.put(`/teacher/resources/${id}`, data),

  deleteResource: (id: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/teacher/resources/${id}`),

  // Resource Comments
  getResourceComments: (resourceId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<ResourceComment>>> =>
    api.get(`/teacher/resources/${resourceId}/comments`, { params }),

  createResourceComment: (resourceId: string, content: string): Promise<ApiResponse<ResourceComment>> =>
    api.post(`/teacher/resources/${resourceId}/comments`, { content }),

  updateResourceComment: (commentId: string, content: string): Promise<ApiResponse<ResourceComment>> =>
    api.put(`/teacher/resources/comments/${commentId}`, { content }),

  deleteResourceComment: (commentId: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/teacher/resources/comments/${commentId}`),

  // Dashboard (Note: This endpoint doesn't exist in backend yet)
  getDashboardStats: (): Promise<ApiResponse<{
    totalCourses: number;
    totalResources: number;
    totalStudents: number;
    recentCourses: Course[];
    recentResources: Resource[];
    recentEnrollments: Enrollment[];
  }>> =>
    api.get('/teacher/dashboard/stats'),
};
