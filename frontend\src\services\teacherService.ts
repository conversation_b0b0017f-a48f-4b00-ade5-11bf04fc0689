import { api } from './api';
import { 
  Teacher,
  Course,
  Resource,
  Student,
  Enrollment,
  ResourceComment,
  ApiResponse,
  CourseCreateData,
  CourseUpdateData,
  ResourceCreateData,
  ResourceUpdateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  departmentId?: string;
  courseId?: string;
  type?: string;
  isPublic?: boolean;
}

export const teacherService = {
  // Profile Management
  getProfile: (): Promise<ApiResponse<Teacher>> =>
    api.get('/teacher/profile'),

  updateProfile: (data: Partial<Teacher>): Promise<ApiResponse<Teacher>> =>
    api.put('/teacher/profile', data),

  // Course Management
  getCourses: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Course>>> =>
    api.get('/teacher/courses', { params }),

  getCourseById: (id: string): Promise<ApiResponse<Course>> =>
    api.get(`/teacher/courses/${id}`),

  createCourse: (data: CourseCreateData): Promise<ApiResponse<Course>> =>
    api.post('/teacher/courses', data),

  updateCourse: (id: string, data: CourseUpdateData): Promise<ApiResponse<Course>> =>
    api.put(`/teacher/courses/${id}`, data),

  deleteCourse: (id: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/teacher/courses/${id}`),

  // Resource Management
  getResources: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Resource>>> =>
    api.get('/teacher/resources', { params }),

  getResourceById: (id: string): Promise<ApiResponse<Resource>> =>
    api.get(`/teacher/resources/${id}`),

  createResource: (data: ResourceCreateData): Promise<ApiResponse<Resource>> =>
    api.post('/teacher/resources', data),

  updateResource: (id: string, data: ResourceUpdateData): Promise<ApiResponse<Resource>> =>
    api.put(`/teacher/resources/${id}`, data),

  deleteResource: (id: string): Promise<ApiResponse<{ message: string }>> =>
    api.delete(`/teacher/resources/${id}`),

  // Student Management
  getCourseStudents: (courseId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Student>>> =>
    api.get(`/teacher/courses/${courseId}/students`, { params }),

  getEnrollments: (params?: QueryParams): Promise<ApiResponse<PaginatedResponse<Enrollment>>> =>
    api.get('/teacher/enrollments', { params }),

  // Resource Comments
  getResourceComments: (resourceId: string, params?: QueryParams): Promise<ApiResponse<PaginatedResponse<ResourceComment>>> =>
    api.get(`/teacher/resources/${resourceId}/comments`, { params }),

  // Dashboard
  getDashboardStats: (): Promise<ApiResponse<{
    totalCourses: number;
    totalResources: number;
    totalStudents: number;
    recentCourses: Course[];
    recentResources: Resource[];
    recentEnrollments: Enrollment[];
  }>> =>
    api.get('/teacher/dashboard/stats'),
};
