import { api } from './api';
import { 
  Resource,
  ResourceComment,
  ApiResponse,
  ResourceCreateData,
  ResourceUpdateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  courseId?: string;
  teacherId?: string;
  isPublic?: boolean;
}

export const resourceService = {
  // Public resource endpoints
  getPublicResources: async (params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<Resource>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.type) searchParams.append('type', params.type);
    if (params.courseId) searchParams.append('courseId', params.courseId);
    if (params.teacherId) searchParams.append('teacherId', params.teacherId);

    const queryString = searchParams.toString();
    const url = queryString ? `/resource/public?${queryString}` : '/resource/public';
    
    return api.get<PaginatedResponse<Resource>>(url);
  },

  getPublicResource: async (id: string): Promise<ApiResponse<Resource>> => {
    return api.get<Resource>(`/resource/public/${id}`);
  },

  // Protected resource endpoints (requires course access)
  getResource: async (id: string): Promise<ApiResponse<Resource>> => {
    return api.get<Resource>(`/resource/${id}`);
  },

  getResourceComments: async (id: string, params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<ResourceComment>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/resource/${id}/comments?${queryString}` : `/resource/${id}/comments`;
    
    return api.get<PaginatedResponse<ResourceComment>>(url);
  },

  // Teacher resource management endpoints
  createResource: async (data: FormData): Promise<ApiResponse<Resource>> => {
    return api.post<Resource>('/resource', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  createVideoResource: async (data: FormData): Promise<ApiResponse<Resource>> => {
    return api.post<Resource>('/resource/video', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  updateResource: async (id: string, data: ResourceUpdateData): Promise<ApiResponse<Resource>> => {
    return api.put<Resource>(`/resource/${id}`, data);
  },

  deleteResource: async (id: string): Promise<ApiResponse<void>> => {
    return api.delete<void>(`/resource/${id}`);
  },
};
