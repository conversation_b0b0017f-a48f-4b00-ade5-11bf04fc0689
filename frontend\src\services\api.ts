import axios, { AxiosRequestConfig, AxiosResponse } from "axios";

// Flag to prevent multiple logout events
let isLoggingOut = false;

// Listen for manual logout events to reset the flag
window.addEventListener("auth:manualLogout", () => {
  isLoggingOut = false;
});

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Important for HTTP-only cookies
});

// Request interceptor to handle FormData
apiClient.interceptors.request.use(
  (config) => {
    // If data is FormData, remove Content-Type header to let axios set it automatically
    // This is crucial for file uploads to work properly
    if (config.data instanceof FormData) {
      delete config.headers["Content-Type"];
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Prevent multiple logout events
      if (!isLoggingOut) {
        isLoggingOut = true;

        // Unauthorized - trigger logout
        // Dispatch custom event to notify AuthContext
        window.dispatchEvent(new CustomEvent("auth:forceLogout"));

        // Redirect to login page after a short delay to allow context to update
        // setTimeout(() => {
        //   window.location.href = "/login";
        //   // Reset flag after redirect
        //   isLoggingOut = false;
        // }, 100);
      }
    }

    // Log the full error response for debugging
    console.log("API Error Response:", error.response?.data);
    console.log("API Error Status:", error.response?.status);
    console.log("API Error Headers:", error.response?.headers);

    // DON'T transform the error - preserve the original axios error structure
    // This allows our error handling utilities to access the full response
    return Promise.reject(error);
  }
);

// Generic API methods
export const api = {
  // GET request
  get: async <T = any>(url: string, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
      const response: AxiosResponse<T> = await apiClient.get(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // POST request
  post: async <T = any>(url: string, data: any = {}, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
      const response: AxiosResponse<T> = await apiClient.post(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // PUT request
  put: async <T = any>(url: string, data: any = {}, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
      const response: AxiosResponse<T> = await apiClient.put(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // DELETE request
  delete: async <T = any>(url: string, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
      const response: AxiosResponse<T> = await apiClient.delete(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // PATCH request
  patch: async <T = any>(url: string, data: any = {}, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
      const response: AxiosResponse<T> = await apiClient.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Re-export services from individual files
export { authService } from "./authService";
export { adminService } from "./adminService";
export { teacherService } from "./teacherService";
export { studentService } from "./studentService";
export { departmentService } from "./departmentService";

export default apiClient;
