import { api } from './api';
import { 
  NewsEvent,
  NewsEventComment,
  ApiResponse,
  NewsEventCommentCreateData,
  NewsEventCommentUpdateData
} from '../common/types';

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isPublished?: boolean;
}

export const newsService = {
  // Public news endpoints
  getPublishedNewsEvents: async (params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<NewsEvent>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.category) searchParams.append('category', params.category);

    const queryString = searchParams.toString();
    const url = queryString ? `/news?${queryString}` : '/news';
    
    return api.get<PaginatedResponse<NewsEvent>>(url);
  },

  getPublishedNewsEvent: async (id: string): Promise<ApiResponse<NewsEvent>> => {
    return api.get<NewsEvent>(`/news/${id}`);
  },

  getNewsEventComments: async (id: string, params: QueryParams = {}): Promise<ApiResponse<PaginatedResponse<NewsEventComment>>> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/news/${id}/comments?${queryString}` : `/news/${id}/comments`;
    
    return api.get<PaginatedResponse<NewsEventComment>>(url);
  },

  // Student comment endpoints
  createStudentComment: async (
    newsEventId: string, 
    data: NewsEventCommentCreateData
  ): Promise<ApiResponse<NewsEventComment>> => {
    return api.post<NewsEventComment>(`/news/${newsEventId}/comments`, data);
  },

  updateStudentComment: async (
    commentId: string, 
    data: NewsEventCommentUpdateData
  ): Promise<ApiResponse<NewsEventComment>> => {
    return api.put<NewsEventComment>(`/news/comments/${commentId}`, data);
  },

  deleteStudentComment: async (commentId: string): Promise<ApiResponse<void>> => {
    return api.delete<void>(`/news/comments/${commentId}`);
  },

  // Teacher comment endpoints
  createTeacherComment: async (
    newsEventId: string, 
    data: NewsEventCommentCreateData
  ): Promise<ApiResponse<NewsEventComment>> => {
    return api.post<NewsEventComment>(`/news/${newsEventId}/comments/teacher`, data);
  },

  updateTeacherComment: async (
    commentId: string, 
    data: NewsEventCommentUpdateData
  ): Promise<ApiResponse<NewsEventComment>> => {
    return api.put<NewsEventComment>(`/news/comments/${commentId}/teacher`, data);
  },

  deleteTeacherComment: async (commentId: string): Promise<ApiResponse<void>> => {
    return api.delete<void>(`/news/comments/${commentId}/teacher`);
  },
};
